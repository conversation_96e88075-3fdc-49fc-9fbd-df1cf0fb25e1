import 'package:flutter/foundation.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/host/data/models/property_type_model.dart';

class MyListingModel {
  final int id;
  final String title;
  final String content;
  final double price;
  final double? weekendPrice;
  final double? weeklyPrice;
  final double? monthlyPrice;
  final String status; // 'active', 'inactive', 'draft', 'pending', 'suspended'
  final bool isAvailable;
  final int views;
  final int bookings;
  final double? rating;
  final int reviewCount;
  final double? revenue;
  final String? mainImageUrl;
  final List<String> galleryImages;
  final ServiceCategory category;
  final PropertyTypeModel? propertyType;
  final int noGuests;
  final int beds;
  final int baths;
  final double? lat;
  final double? lon;
  final String? address;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastBookingAt;
  final List<String> facilities;
  final Map<String, dynamic>? analytics;
  final bool hasActiveReservations;
  final int pendingReservations;
  final String? rejectionReason;
  final DateTime? publishedAt;

  MyListingModel({
    required this.id,
    required this.title,
    required this.content,
    required this.price,
    this.weekendPrice,
    this.weeklyPrice,
    this.monthlyPrice,
    required this.status,
    required this.isAvailable,
    required this.views,
    required this.bookings,
    this.rating,
    required this.reviewCount,
    this.revenue,
    this.mainImageUrl,
    required this.galleryImages,
    required this.category,
    this.propertyType,
    required this.noGuests,
    required this.beds,
    required this.baths,
    this.lat,
    this.lon,
    this.address,
    required this.createdAt,
    required this.updatedAt,
    this.lastBookingAt,
    required this.facilities,
    this.analytics,
    required this.hasActiveReservations,
    required this.pendingReservations,
    this.rejectionReason,
    this.publishedAt,
  });

  factory MyListingModel.fromJson(Map<String, dynamic> json) {
    try {
      return MyListingModel(
        id: json['id'] ?? 0,
        title: json['title']?.toString() ?? '',
        content: json['content']?.toString() ?? '',
        price: double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
        weekendPrice: double.tryParse(json['weekend_price']?.toString() ?? '0'),
        weeklyPrice: double.tryParse(json['week_price']?.toString() ?? '0'),
        monthlyPrice: double.tryParse(json['month_price']?.toString() ?? '0'),
        status: json['status']?.toString() ?? 'draft',
        isAvailable: json['is_available'] ?? true,
        views: json['views'] ?? 0,
        bookings: json['bookings'] ?? 0,
        rating: double.tryParse(json['rating']?.toString() ?? '0'),
        reviewCount: json['review_count'] ?? 0,
        revenue: double.tryParse(json['revenue']?.toString() ?? '0'),
        mainImageUrl: json['main_image_url']?.toString(),
        galleryImages: _parseStringList(json['gallery_images']),
        category: ServiceCategory.fromJson(json['category'] ?? {}),
        propertyType: json['property_type'] != null
            ? PropertyTypeModel.fromJson(json['property_type'])
            : null,
        noGuests: json['no_guests'] ?? 1,
        beds: json['beds'] ?? 1,
        baths: json['baths'] ?? 1,
        lat: double.tryParse(json['lat']?.toString() ?? '0'),
        lon: double.tryParse(json['lon']?.toString() ?? '0'),
        address: json['address']?.toString(),
        createdAt: _parseDateTime(json['created_at']) ?? DateTime.now(),
        updatedAt: _parseDateTime(json['updated_at']) ?? DateTime.now(),
        lastBookingAt: _parseDateTime(json['last_booking_at']),
        facilities: _parseStringList(json['facilities']),
        analytics: json['analytics'],
        hasActiveReservations: json['has_active_reservations'] ?? false,
        pendingReservations: json['pending_reservations'] ?? 0,
        rejectionReason: json['rejection_reason']?.toString(),
        publishedAt: _parseDateTime(json['published_at']),
      );
    } catch (e) {
      debugPrint('❌ Error parsing MyListingModel: $e');
      debugPrint('📋 JSON data: $json');
      rethrow;
    }
  }

  /// Helper method to safely parse DateTime fields
  static DateTime? _parseDateTime(dynamic dateTime) {
    if (dateTime == null) return null;

    if (dateTime is String) {
      try {
        return DateTime.parse(dateTime);
      } catch (e) {
        debugPrint('⚠️ Failed to parse DateTime string: $dateTime');
        return null;
      }
    } else if (dateTime is Map) {
      // If it's a Map (like a DateTime object), try to extract a string representation
      final dateString = dateTime['date']?.toString() ??
                        dateTime['formatted']?.toString() ??
                        dateTime.toString();
      try {
        return DateTime.parse(dateString);
      } catch (e) {
        debugPrint('⚠️ Failed to parse DateTime from Map: $dateTime');
        return null;
      }
    } else {
      // For any other type, try to convert to string and parse
      try {
        return DateTime.parse(dateTime.toString());
      } catch (e) {
        debugPrint('⚠️ Failed to parse DateTime from ${dateTime.runtimeType}: $dateTime');
        return null;
      }
    }
  }

  /// Helper method to safely parse string lists
  static List<String> _parseStringList(dynamic list) {
    if (list == null) return [];

    if (list is List) {
      return list.map((item) => item?.toString() ?? '').toList();
    } else {
      debugPrint('⚠️ Expected List but got ${list.runtimeType}: $list');
      return [];
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'price': price,
      'weekend_price': weekendPrice,
      'week_price': weeklyPrice,
      'month_price': monthlyPrice,
      'status': status,
      'is_available': isAvailable,
      'views': views,
      'bookings': bookings,
      'rating': rating,
      'review_count': reviewCount,
      'revenue': revenue,
      'main_image_url': mainImageUrl,
      'gallery_images': galleryImages,
      'category': category.toJson(),
      'property_type': propertyType?.toJson(),
      'no_guests': noGuests,
      'beds': beds,
      'baths': baths,
      'lat': lat,
      'lon': lon,
      'address': address,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_booking_at': lastBookingAt?.toIso8601String(),
      'facilities': facilities,
      'analytics': analytics,
      'has_active_reservations': hasActiveReservations,
      'pending_reservations': pendingReservations,
      'rejection_reason': rejectionReason,
      'published_at': publishedAt?.toIso8601String(),
    };
  }

  // Helper getters
  String get statusDisplayName {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'inactive':
        return 'غير نشط';
      case 'draft':
        return 'مسودة';
      case 'pending':
        return 'قيد المراجعة';
      case 'suspended':
        return 'معلق';
      default:
        return status;
    }
  }

  bool get isPublished => status == 'active' || status == 'inactive';
  bool get isDraft => status == 'draft';
  bool get isPending => status == 'pending';
  bool get isSuspended => status == 'suspended';
  bool get isActive => status == 'active' && isAvailable;

  String get priceDisplay {
    return '${price.toStringAsFixed(0)} ر.س';
  }

  String get guestCapacityDisplay {
    return '$noGuests ${noGuests == 1 ? 'ضيف' : 'ضيوف'}';
  }

  String get roomsDisplay {
    final parts = <String>[];
    if (beds > 0) parts.add('$beds ${beds == 1 ? 'غرفة' : 'غرف'}');
    if (baths > 0) parts.add('$baths ${baths == 1 ? 'حمام' : 'حمامات'}');
    return parts.join(' • ');
  }

  String get performanceDisplay {
    final parts = <String>[];
    if (views > 0) parts.add('$views مشاهدة');
    if (bookings > 0) parts.add('$bookings حجز');
    if (rating != null) parts.add('${rating!.toStringAsFixed(1)} ⭐');
    return parts.join(' • ');
  }

  String get revenueDisplay {
    if (revenue == null || revenue == 0) return '0 ر.س';
    return '${revenue!.toStringAsFixed(0)} ر.س';
  }

  String get timeSinceCreated {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    }
  }

  String get lastActivityDisplay {
    if (lastBookingAt != null) {
      final now = DateTime.now();
      final difference = now.difference(lastBookingAt!);
      
      if (difference.inDays > 0) {
        return 'آخر حجز منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
      } else {
        return 'آخر حجز اليوم';
      }
    }
    return 'لا توجد حجوزات بعد';
  }

  // Copy with method for updates
  MyListingModel copyWith({
    int? id,
    String? title,
    String? content,
    double? price,
    double? weekendPrice,
    double? weeklyPrice,
    double? monthlyPrice,
    String? status,
    bool? isAvailable,
    int? views,
    int? bookings,
    double? rating,
    int? reviewCount,
    double? revenue,
    String? mainImageUrl,
    List<String>? galleryImages,
    ServiceCategory? category,
    PropertyTypeModel? propertyType,
    int? noGuests,
    int? beds,
    int? baths,
    double? lat,
    double? lon,
    String? address,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastBookingAt,
    List<String>? facilities,
    Map<String, dynamic>? analytics,
    bool? hasActiveReservations,
    int? pendingReservations,
    String? rejectionReason,
    DateTime? publishedAt,
  }) {
    return MyListingModel(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      price: price ?? this.price,
      weekendPrice: weekendPrice ?? this.weekendPrice,
      weeklyPrice: weeklyPrice ?? this.weeklyPrice,
      monthlyPrice: monthlyPrice ?? this.monthlyPrice,
      status: status ?? this.status,
      isAvailable: isAvailable ?? this.isAvailable,
      views: views ?? this.views,
      bookings: bookings ?? this.bookings,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      revenue: revenue ?? this.revenue,
      mainImageUrl: mainImageUrl ?? this.mainImageUrl,
      galleryImages: galleryImages ?? this.galleryImages,
      category: category ?? this.category,
      propertyType: propertyType ?? this.propertyType,
      noGuests: noGuests ?? this.noGuests,
      beds: beds ?? this.beds,
      baths: baths ?? this.baths,
      lat: lat ?? this.lat,
      lon: lon ?? this.lon,
      address: address ?? this.address,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastBookingAt: lastBookingAt ?? this.lastBookingAt,
      facilities: facilities ?? this.facilities,
      analytics: analytics ?? this.analytics,
      hasActiveReservations: hasActiveReservations ?? this.hasActiveReservations,
      pendingReservations: pendingReservations ?? this.pendingReservations,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      publishedAt: publishedAt ?? this.publishedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MyListingModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MyListingModel(id: $id, title: $title, status: $status, price: $price)';
  }
}
