import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:latlong2/latlong.dart';
import 'package:video_player/video_player.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/routing/routes_keys.dart';


import 'package:gather_point/feature/host/presentation/cubit/property_creation_cubit.dart';
import 'package:gather_point/feature/home/<USER>/Data Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/host/data/models/property_type_model.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';

import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/feature/host/services/property_edit_service.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/feature/host/widgets/location_map_picker.dart';
import 'package:gather_point/feature/host/services/geocoding_service.dart';

/// Simple wizard-style property creation form
class CreatePropertyWizard extends StatelessWidget {
  const CreatePropertyWizard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        try {
          final cubit = PropertyCreationCubit(
            getIt<PropertiesApiService>(),
            getIt.isRegistered<PropertyEditService>() ? getIt<PropertyEditService>() : null,
          );
          // Load initial data immediately
          cubit.loadInitialData();
          return cubit;
        } catch (e) {
          debugPrint('Error creating PropertyCreationCubit: $e');
          final fallbackCubit = PropertyCreationCubit(getIt<PropertiesApiService>());
          fallbackCubit.loadInitialData();
          return fallbackCubit;
        }
      },
      child: const _CreatePropertyWizardContent(),
    );
  }
}

class _CreatePropertyWizardContent extends StatefulWidget {
  const _CreatePropertyWizardContent();

  @override
  State<_CreatePropertyWizardContent> createState() => _CreatePropertyWizardContentState();
}

class _CreatePropertyWizardContentState extends State<_CreatePropertyWizardContent> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 7; // Increased to include booking rules and tourism permit step

  // Form controllers
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _guestsController = TextEditingController();
  final _bedsController = TextEditingController();
  final _bathsController = TextEditingController();
  final _bookingRulesController = TextEditingController();
  final _tourismPermitController = TextEditingController();

  // Form data
  int? _selectedCategory;
  int? _selectedPropertyType;
  int? _selectedCancellationPolicy;
  final List<int> _selectedFacilities = [];

  // Media and location
  final List<File> _imageGallery = [];
  File? _videoFile;
  File? _tourismPermitDocument;
  LatLng? _selectedLocation;
  String? _locationAddress;
  final ImagePicker _picker = ImagePicker();
  VideoPlayerController? _videoController;

  // Auto-save and server sync
  Timer? _autoSaveTimer;
  bool _isAutoSaving = false;
  bool _isSubmitting = false;
  DateTime? _lastSyncTime;

  @override
  void initState() {
    super.initState();
    _setupAutoSave();
    _loadDraftData();

    // Add a delay to ensure the cubit is properly initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cubit = context.read<PropertyCreationCubit>();
      debugPrint('InitState - Current cubit state: ${cubit.state.runtimeType}');

      // Test API services
      _testApiServices();

      // If still in initial state, try loading data again
      if (cubit.state is PropertyCreationInitial) {
        debugPrint('InitState - State is initial, loading data...');
        cubit.loadInitialData();
      }
    });
  }

  Future<void> _loadDraftData() async {
    // Try to load any existing draft data
    try {
      final cubit = context.read<PropertyCreationCubit>();
      final draftData = await cubit.loadPropertyDraft();

      if (draftData != null) {
        _populateFormFromDraft(draftData);
      }
    } catch (e) {
      debugPrint('Failed to load draft data: $e');
    }
  }

  Future<void> _testApiServices() async {
    try {
      debugPrint('Testing API services...');
      final propertiesService = getIt<PropertiesApiService>();

      // Test each service individually
      try {
        final categories = await propertiesService.getServiceCategories();
        debugPrint('Categories API test: SUCCESS - ${categories.length} items');
      } catch (e) {
        debugPrint('Categories API test: FAILED - $e');
      }

      try {
        final facilities = await propertiesService.getFacilities();
        debugPrint('Facilities API test: SUCCESS - ${facilities.length} items');
      } catch (e) {
        debugPrint('Facilities API test: FAILED - $e');
      }

      try {
        final propertyTypes = await propertiesService.getPropertyTypes();
        debugPrint('Property Types API test: SUCCESS - ${propertyTypes.length} items');
      } catch (e) {
        debugPrint('Property Types API test: FAILED - $e');
      }

      try {
        final cancellationPolicies = await propertiesService.getCancellationPolicies();
        debugPrint('Cancellation Policies API test: SUCCESS - ${cancellationPolicies.length} items');
      } catch (e) {
        debugPrint('Cancellation Policies API test: FAILED - $e');
      }

    } catch (e) {
      debugPrint('API services test failed: $e');
    }
  }

  void _populateFormFromProperty(PropertyItemModel property) {
    setState(() {
      _titleController.text = property.title;
      _descriptionController.text = property.content;
      _priceController.text = property.price.toString();
      _guestsController.text = property.noGuests.toString();
      _bedsController.text = property.beds.toString();
      _bathsController.text = property.baths.toString();
      _selectedCategory = property.serviceCategoryId;
      // Note: property type and cancellation policy would need to be added to PropertyItemModel
      // For now, we'll leave them as null and they'll be selected by the user
    });
  }

  void _populateFormFromDraft(Map<String, dynamic> draftData) {
    setState(() {
      // Basic info
      if (draftData['title'] != null) _titleController.text = draftData['title'];
      if (draftData['content'] != null) _descriptionController.text = draftData['content'];
      if (draftData['price'] != null) _priceController.text = draftData['price'].toString();
      if (draftData['no_guests'] != null) _guestsController.text = draftData['no_guests'].toString();
      if (draftData['beds'] != null) _bedsController.text = draftData['beds'].toString();
      if (draftData['baths'] != null) _bathsController.text = draftData['baths'].toString();

      // Categories and types
      if (draftData['service_category_id'] != null) _selectedCategory = draftData['service_category_id'];
      if (draftData['property_type_id'] != null) _selectedPropertyType = draftData['property_type_id'];
      if (draftData['cancellation_policy_id'] != null) _selectedCancellationPolicy = draftData['cancellation_policy_id'];

      // Facilities
      if (draftData['facility_ids'] != null && draftData['facility_ids'] is List) {
        _selectedFacilities.clear();
        _selectedFacilities.addAll(List<int>.from(draftData['facility_ids']));
      }

      // Location
      if (draftData['lat'] != null && draftData['lon'] != null) {
        _selectedLocation = LatLng(draftData['lat'], draftData['lon']);
      }
      if (draftData['address'] != null) _locationAddress = draftData['address'];

      // Current step
      if (draftData['current_step'] != null) {
        _currentStep = draftData['current_step'];
        _pageController.animateToPage(
          _currentStep,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _guestsController.dispose();
    _bedsController.dispose();
    _bathsController.dispose();
    _autoSaveTimer?.cancel();
    _videoController?.dispose();
    super.dispose();
  }

  void _setupAutoSave() {
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _performAutoSave();
    });
  }

  Future<void> _performAutoSave() async {
    if (_isAutoSaving || _isSubmitting) return;

    // Debounce auto-save to prevent excessive calls
    if (_lastSyncTime != null &&
        DateTime.now().difference(_lastSyncTime!).inSeconds < 15) {
      return;
    }

    setState(() => _isAutoSaving = true);

    try {
      // Save current step data to server
      await _saveStepToServer(_currentStep);
      _lastSyncTime = DateTime.now();

      if (mounted) {
        final timeStr = _lastSyncTime != null
            ? '${_lastSyncTime!.hour.toString().padLeft(2, '0')}:${_lastSyncTime!.minute.toString().padLeft(2, '0')}'
            : '';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.cloud_done, color: Colors.white, size: 16),
                const SizedBox(width: 8),
                Text('Auto-saved $timeStr', style: AppTextStyles.font12Medium.copyWith(color: Colors.white)),
              ],
            ),
            backgroundColor: Colors.green.withValues(alpha: 0.8),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
            margin: const EdgeInsets.only(bottom: 100, left: 16, right: 16),
          ),
        );
      }
    } catch (e) {
      debugPrint('Auto-save failed: $e');
    } finally {
      setState(() => _isAutoSaving = false);
    }
  }

  Future<void> _saveStepToServer(int step) async {
    // Implementation for saving step data to server
    final stepData = _getStepData(step);
    if (stepData.isNotEmpty) {
      try {
        // Save draft data using the cubit
        final cubit = context.read<PropertyCreationCubit>();
        await cubit.savePropertyAsDraft(0, stepData); // Use 0 for new property
      } catch (e) {
        debugPrint('Failed to save draft: $e');
      }
    }
  }

  Map<String, dynamic> _getStepData(int step) {
    return {
      'current_step': _currentStep,
      'title': _titleController.text,
      'content': _descriptionController.text,
      'price': _priceController.text.isNotEmpty ? double.tryParse(_priceController.text) : null,
      'no_guests': _guestsController.text.isNotEmpty ? int.tryParse(_guestsController.text) : null,
      'beds': _bedsController.text.isNotEmpty ? int.tryParse(_bedsController.text) : null,
      'baths': _bathsController.text.isNotEmpty ? int.tryParse(_bathsController.text) : null,
      'service_category_id': _selectedCategory,
      'property_type_id': _selectedPropertyType,
      'cancelation_policy_id': _selectedCancellationPolicy,
      'facilities': _selectedFacilities,
      'lat': _selectedLocation?.latitude,
      'lon': _selectedLocation?.longitude,
      'address': _locationAddress,
      'booking_rules': _bookingRulesController.text,
      'tourism_permit_number': _tourismPermitController.text,
      'step_name': _getStepName(step),
      'images_count': _imageGallery.length,
      'has_video': _videoFile != null,
    };
  }

  String _getStepName(int step) {
    switch (step) {
      case 0: return 'basic_info';
      case 1: return 'category_selection';
      case 2: return 'booking_rules_tourism';
      case 3: return 'details';
      case 4: return 'location';
      case 5: return 'gallery';
      default: return 'unknown';
    }
  }

  void _nextStep() {
    if (_validateCurrentStep()) {
      if (_currentStep < _totalSteps - 1) {
        setState(() => _currentStep++);
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        // Save current step to server
        _saveStepToServer(_currentStep - 1);
      }
    } else {
      _showValidationErrors();
    }
  }

  bool _validateCurrentStep() {
    final s = S.of(context);

    switch (_currentStep) {
      case 0: // Basic Information
        if (_titleController.text.trim().isEmpty) {
          _showErrorSnackBar(s.propertyTitleRequired);
          return false;
        }
        if (_titleController.text.trim().length < 3) {
          _showErrorSnackBar(s.propertyTitleTooShort);
          return false;
        }
        if (_descriptionController.text.trim().isEmpty) {
          _showErrorSnackBar(s.propertyDescriptionRequired);
          return false;
        }
        if (_descriptionController.text.trim().length < 10) {
          _showErrorSnackBar(s.propertyDescriptionTooShort);
          return false;
        }
        if (_priceController.text.trim().isEmpty) {
          _showErrorSnackBar(s.priceRequired);
          return false;
        }
        final price = double.tryParse(_priceController.text);
        if (price == null || price <= 0) {
          _showErrorSnackBar(s.priceInvalid);
          return false;
        }
        if (price < 50) {
          _showErrorSnackBar(s.priceMinimum);
          return false;
        }
        break;

      case 1: // Category & Type
        if (_selectedCategory == null) {
          _showErrorSnackBar(s.categoryRequired);
          return false;
        }
        if (_selectedPropertyType == null) {
          _showErrorSnackBar(s.propertyTypeRequired);
          return false;
        }
        if (_selectedCancellationPolicy == null) {
          _showErrorSnackBar(s.cancellationPolicyRequired);
          return false;
        }
        break;

      case 2: // Booking Rules & Tourism Permit
        // Booking rules are optional, but if provided should be meaningful
        if (_bookingRulesController.text.trim().isNotEmpty &&
            _bookingRulesController.text.trim().length < 10) {
          _showErrorSnackBar(s.bookingRulesMinLength);
          return false;
        }
        // Tourism permit number is required
        if (_tourismPermitController.text.trim().isEmpty) {
          _showErrorSnackBar(s.tourismPermitNumberRequired);
          return false;
        }
        if (_tourismPermitController.text.trim().length < 5) {
          _showErrorSnackBar(s.tourismPermitNumberMinLength);
          return false;
        }
        // Tourism permit document is required
        if (_tourismPermitDocument == null) {
          _showErrorSnackBar(s.tourismPermitDocumentRequired);
          return false;
        }
        debugPrint('✅ Step 2 validation passed - Tourism permit: ${_tourismPermitController.text.trim()}');
        break;

      case 3: // Property Details
        if (_guestsController.text.trim().isEmpty) {
          _showErrorSnackBar(s.guestsRequired);
          return false;
        }
        final guests = int.tryParse(_guestsController.text);
        if (guests == null || guests <= 0 || guests > 20) {
          _showErrorSnackBar(s.guestsInvalid);
          return false;
        }
        if (_bedsController.text.trim().isEmpty) {
          _showErrorSnackBar(s.bedsRequired);
          return false;
        }
        final beds = int.tryParse(_bedsController.text);
        if (beds == null || beds <= 0 || beds > 10) {
          _showErrorSnackBar(s.bedsInvalid);
          return false;
        }
        if (_bathsController.text.trim().isEmpty) {
          _showErrorSnackBar(s.bathsRequired);
          return false;
        }
        final baths = int.tryParse(_bathsController.text);
        if (baths == null || baths <= 0 || baths > 10) {
          _showErrorSnackBar(s.bathsInvalid);
          return false;
        }
        if (_selectedFacilities.isEmpty) {
          _showErrorSnackBar(s.facilitiesRequired);
          return false;
        }
        // Debug: Print selected facilities
        debugPrint('✅ Facilities validation passed. Selected: $_selectedFacilities');
        break;

      case 4: // Location
        if (_selectedLocation == null) {
          _showErrorSnackBar(s.locationRequired);
          return false;
        }
        break;

      case 5: // Gallery
        if (_imageGallery.isEmpty) {
          _showErrorSnackBar(s.imagesRequired);
          return false;
        }
        if (_imageGallery.length < 3) {
          _showErrorSnackBar(s.imagesMinimum);
          return false;
        }
        break;
    }

    return true;
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.only(bottom: 100, left: 16, right: 16),
      ),
    );
    HapticFeedback.lightImpact();
  }

  void _showValidationErrors() {
    // Additional validation error handling if needed
  }

  void _clearDraft() {
    try {
      // Clear all form controllers
      _titleController.clear();
      _descriptionController.clear();
      _priceController.clear();
      _guestsController.clear();
      _bedsController.clear();
      _bathsController.clear();
      _bookingRulesController.clear();
      _tourismPermitController.clear();

      // Reset form data
      setState(() {
        _selectedCategory = null;
        _selectedPropertyType = null;
        _selectedCancellationPolicy = null;
        _selectedFacilities.clear();
        _selectedLocation = null;
        _locationAddress = null;
        _imageGallery.clear();
        _videoFile = null;
        _tourismPermitDocument = null;
        _currentStep = 0;
      });

      // Clear draft from storage via cubit
      context.read<PropertyCreationCubit>().clearDraft();

      debugPrint('Draft cleared successfully');
    } catch (e) {
      debugPrint('Error clearing draft: $e');
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() => _currentStep--);
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _submitForm() async {
    final s = S.of(context);

    // Prevent multiple submissions
    if (_isSubmitting) {
      debugPrint('⚠️ Form submission already in progress, ignoring duplicate click');
      return;
    }

    if (!_validateCurrentStep()) {
      _showErrorSnackBar(s.validationFailed);
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      // Collect all form data
      final propertyData = {
        'title': _titleController.text.trim(),
        'content': _descriptionController.text.trim(),
        'price': double.parse(_priceController.text),
        'no_guests': int.parse(_guestsController.text),
        'beds': int.parse(_bedsController.text),
        'baths': int.parse(_bathsController.text),
        'service_category_id': _selectedCategory ?? 1,
        'property_type_id': _selectedPropertyType ?? 1,
        'cancelation_policy_id': _selectedCancellationPolicy ?? 1,
        'facilities': _selectedFacilities,
        'lat': _selectedLocation?.latitude ?? 24.7136,
        'lon': _selectedLocation?.longitude ?? 46.6753,
        'booking_rules': _bookingRulesController.text.trim(),
        'tourism_permit_number': _tourismPermitController.text.trim(),
      };

      // Debug: Print the property data to see what we're sending
      debugPrint('🚀 Creating property with data: $propertyData');
      debugPrint('📋 Selected facilities: $_selectedFacilities (count: ${_selectedFacilities.length}, type: ${_selectedFacilities.runtimeType})');
      debugPrint('🖼️ Images count: ${_imageGallery.length}');
      debugPrint('🎥 Video: ${_videoFile != null ? 'Yes' : 'No'}');
      debugPrint('📄 Tourism permit document: ${_tourismPermitDocument != null ? 'Yes' : 'No'}');

      // Use the cubit to create property - this will trigger the BlocConsumer
      // The _isSubmitting flag will be reset in the BlocConsumer listener
      context.read<PropertyCreationCubit>().createProperty(
        propertyData: propertyData,
        mainImage: _imageGallery.isNotEmpty ? _imageGallery.first : null,
        video: _videoFile,
        galleryImages: _imageGallery.length > 1 ? _imageGallery.sublist(1) : null,
        tourismPermitDocument: _tourismPermitDocument,
      );
    } catch (e) {
      // Handle any synchronous errors (like parsing errors)
      debugPrint('❌ Error in _submitForm: $e');
      if (mounted) {
        setState(() => _isSubmitting = false);
        _showErrorSnackBar(s.errorOccurred);
      }
    }
  }





  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);

    // Performance monitoring
    final stopwatch = Stopwatch()..start();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      stopwatch.stop();
      if (stopwatch.elapsedMilliseconds > 16) { // More than one frame (16ms)
        debugPrint('⚠️ PropertyCreationWizard build took ${stopwatch.elapsedMilliseconds}ms');
      }
    });

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          s.createProperty,
          style: theme.textTheme.titleLarge,
        ),
        backgroundColor: theme.appBarTheme.backgroundColor,
        foregroundColor: theme.appBarTheme.foregroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.appBarTheme.foregroundColor,
          ),
          onPressed: () {
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              context.go(RoutesKeys.kReelsViewTab);
            }
          },
        ),
        actions: [
          // Debug button to reload data
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              debugPrint('🔄 Manual refresh triggered');
              final cubit = context.read<PropertyCreationCubit>();
              debugPrint('🔄 Current state before refresh: ${cubit.state.runtimeType}');
              cubit.loadInitialData();
            },
          ),
        ],
      ),
      body: BlocConsumer<PropertyCreationCubit, PropertyCreationState>(
        listener: (context, state) {
          if (state is PropertyCreationLoading) {
            // Ensure _isSubmitting is true when loading state is emitted
            // This handles edge cases where the state might change without going through _submitForm
            if (!_isSubmitting) {
              setState(() => _isSubmitting = true);
            }
          } else if (state is PropertyCreationSuccess) {
            setState(() => _isSubmitting = false);

            // Clear draft data on successful creation
            _clearDraft();

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(s.propertyCreatedSuccessfully),
                backgroundColor: Colors.green,
              ),
            );
            // Safe navigation back
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop(true);
            } else {
              context.go(RoutesKeys.kReelsViewTab);
            }
          } else if (state is PropertyCreationError) {
            setState(() => _isSubmitting = false);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is PropertyCreationDataLoaded && state.existingProperty != null) {
            // Populate form with existing property data
            _populateFormFromProperty(state.existingProperty!);
          }
        },
        builder: (context, state) {
          // Debug: Print current state
          debugPrint('PropertyCreationWizard - Current state: ${state.runtimeType}');
          if (state is PropertyCreationDataLoaded) {
            debugPrint('Categories count: ${state.categories.length}');
            debugPrint('Property types count: ${state.propertyTypes.length}');
            debugPrint('Cancellation policies count: ${state.cancellationPolicies.length}');
            debugPrint('Facilities count: ${state.facilities.length}');
          }

          // Show loading only during form submission, not during initial data loading
          if (state is PropertyCreationLoading && _isSubmitting) {
            return Container(
              color: theme.scaffoldBackgroundColor,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: theme.primaryColor,
                      strokeWidth: 3,
                    ),
                    const SizedBox(height: 24),
                    Text(
                      s.creatingProperty,
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      s.pleaseWaitProcessing,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          }

          return Column(
            children: [
              // Progress indicator
              _buildProgressIndicator(),

              // Form content
              Expanded(
                child: PageView(
                  controller: _pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    _buildBasicInfoStep(state),
                    _buildCategoryStep(state),
                    _buildBookingRulesStep(state),
                    _buildDetailsStep(state),
                    _buildLocationStep(state),
                    _buildGalleryStep(state),
                    _buildReviewStep(),
                  ],
                ),
              ),

              // Navigation buttons
              _buildNavigationButtons(state),
            ],
          );
        },
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: List.generate(_totalSteps, (index) {
          final isActive = index <= _currentStep;
          final isCompleted = index < _currentStep;

          return Expanded(
            child: Container(
              height: 4,
              margin: EdgeInsets.only(right: index < _totalSteps - 1 ? 8 : 0),
              decoration: BoxDecoration(
                color: isCompleted
                    ? Colors.green
                    : isActive
                        ? theme.primaryColor
                        : theme.dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildBasicInfoStep(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced header with theme colors
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: theme.primaryColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(Icons.info_outline, color: theme.colorScheme.onPrimary, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        s.basicInformation,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        s.basicInformationDesc,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Enhanced form fields
          _buildEnhancedTextField(
            controller: _titleController,
            label: s.propertyTitle,
            hint: s.propertyTitleHint,
            icon: Icons.title,
            maxLength: 100,
          ),
          const SizedBox(height: 20),

          _buildEnhancedTextField(
            controller: _descriptionController,
            label: s.propertyDescription,
            hint: s.propertyDescriptionHint,
            icon: Icons.description,
            maxLines: 4,
            maxLength: 500,
          ),
          const SizedBox(height: 20),

          _buildEnhancedTextField(
            controller: _priceController,
            label: s.pricePerNight,
            hint: s.priceHint,
            icon: Icons.attach_money,
            keyboardType: TextInputType.number,
            prefixText: 'SAR ',
          ),

          const SizedBox(height: 16),

          // Price guidance card
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: theme.primaryColor.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.lightbulb_outline, color: theme.primaryColor, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    s.priceGuidance,
                    style: theme.textTheme.bodySmall?.copyWith(color: theme.primaryColor),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryStep(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced header with theme colors
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: theme.primaryColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(Icons.category, color: theme.colorScheme.onPrimary, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        s.categoryAndType,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        s.categoryTypeDescription,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Enhanced selections
          _buildEnhancedCategorySelection(state),
          const SizedBox(height: 20),

          _buildEnhancedPropertyTypeSelection(state),
          const SizedBox(height: 20),

          _buildEnhancedCancellationPolicySelection(state),
        ],
      ),
    );
  }

  Widget _buildBookingRulesStep(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced header with theme colors
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: theme.primaryColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(Icons.rule, color: theme.colorScheme.onPrimary, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${s.bookingRules} & ${s.tourismPermitNumber}',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        s.bookingRulesDescription,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Booking Rules Section
          _buildEnhancedTextField(
            controller: _bookingRulesController,
            label: s.bookingRules,
            hint: s.bookingRulesHint,
            icon: Icons.rule,
            maxLines: 4,
            maxLength: 500,
          ),
          const SizedBox(height: 20),

          // Tourism Permit Section
          _buildEnhancedTextField(
            controller: _tourismPermitController,
            label: s.tourismPermitNumber,
            hint: s.tourismPermitNumberHint,
            icon: Icons.verified_user,
            maxLength: 50,
          ),
          const SizedBox(height: 20),

          // Tourism Permit Document Section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: theme.dividerColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.attach_file, color: theme.primaryColor, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      s.tourismPermitDocument,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  s.tourismPermitDocumentHint,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 16),

                if (_tourismPermitDocument != null) ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.check_circle, color: Colors.green, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                s.documentSelected,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.green,
                                ),
                              ),
                              Text(
                                _tourismPermitDocument!.path.split('/').last,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          onPressed: _removeTourismPermitDocument,
                          icon: const Icon(Icons.close, color: Colors.red, size: 20),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                ],

                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: _pickTourismPermitDocument,
                    icon: Icon(_tourismPermitDocument != null ? Icons.edit : Icons.upload_file),
                    label: Text(_tourismPermitDocument != null ? s.changeDocument : s.uploadDocument),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      side: BorderSide(color: theme.primaryColor),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Info card about tourism permit
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: theme.primaryColor.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: theme.primaryColor, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    s.tourismPermitInfo,
                    style: theme.textTheme.bodySmall?.copyWith(color: theme.primaryColor),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsStep(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced header with theme colors
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: theme.primaryColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(Icons.home_work, color: theme.colorScheme.onPrimary, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        s.propertyDetails,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        s.propertyDetailsDescription,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _guestsController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: s.maxGuests,
                    border: const OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _bedsController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: s.bedrooms,
                    border: const OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _bathsController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: s.bathrooms,
                    border: const OutlineInputBorder(),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          
          // Facilities selection
          _buildFacilitiesSelection(state),
        ],
      ),
    );
  }

  Widget _buildReviewStep() {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.reviewAndSubmit,
            style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),
          
          _buildReviewCard(s.title, _titleController.text),
          _buildReviewCard(s.description, _descriptionController.text),
          _buildReviewCard(s.pricePerNight, '${_priceController.text} SAR/night'),
          _buildReviewCard(s.guests, _guestsController.text),
          _buildReviewCard(s.bedrooms, _bedsController.text),
          _buildReviewCard(s.bathrooms, _bathsController.text),
          _buildReviewCard(s.category, _getSelectedCategoryName()),
          _buildReviewCard(s.propertyType, _getSelectedPropertyTypeName()),
          _buildReviewCard(s.cancellationPolicy, _getSelectedCancellationPolicyName()),
          _buildReviewCard(s.facilities, _getSelectedFacilitiesNames()),
          _buildReviewCard(s.bookingRules, _bookingRulesController.text.trim().isEmpty ? s.notProvided : _bookingRulesController.text.trim()),
          _buildReviewCard(s.tourismPermitNumber, _tourismPermitController.text.trim().isEmpty ? s.notProvided : _tourismPermitController.text.trim()),
          _buildReviewCard(s.tourismPermitDocument, _tourismPermitDocument != null ? s.uploaded : s.notUploaded),
          _buildReviewCard(s.location, _locationAddress ?? s.notSelected),
          _buildReviewCard(s.photos, '${_imageGallery.length} ${s.images}'),
          _buildReviewCard(s.video, _videoFile != null ? s.added : s.notAdded),
        ],
      ),
    );
  }

  Widget _buildLocationStep(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.locationAndAddress,
            style: AppTextStyles.font20Bold,
          ),
          const SizedBox(height: 24),

          // Location picker
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.location_on, color: theme.primaryColor),
                      const SizedBox(width: 8),
                      Text(s.propertyLocation),
                      const Spacer(),
                      if (_selectedLocation != null)
                        const Icon(Icons.check_circle, color: Colors.green),
                    ],
                  ),
                  const SizedBox(height: 16),

                  if (_selectedLocation != null) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.check_circle, color: Colors.green, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(s.locationSelected, style: const TextStyle(fontWeight: FontWeight.bold)),
                                if (_locationAddress != null)
                                  Text(_locationAddress!, style: const TextStyle(fontSize: 12)),
                                Text(
                                  'Lat: ${_selectedLocation!.latitude.toStringAsFixed(6)}, '
                                  'Lng: ${_selectedLocation!.longitude.toStringAsFixed(6)}',
                                  style: const TextStyle(fontSize: 10, color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  ElevatedButton.icon(
                    onPressed: _openLocationPicker,
                    icon: Icon(_selectedLocation != null ? Icons.edit_location : Icons.add_location),
                    label: Text(_selectedLocation != null ? s.changeLocation : s.selectLocation),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.primaryColor,
                      foregroundColor: theme.colorScheme.onPrimary,
                      minimumSize: const Size(double.infinity, 48),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGalleryStep(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.photosAndVideo,
            style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),

          // Image gallery section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.photo_library, color: Color(0xFFFEC53A)),
                      const SizedBox(width: 8),
                      Text(s.propertyPhotos),
                      const Spacer(),
                      Text('${_imageGallery.length}/10', style: const TextStyle(color: Colors.grey)),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Image grid
                  if (_imageGallery.isNotEmpty) ...[
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: _imageGallery.length + 1,
                      cacheExtent: 200, // Cache images for better performance
                      itemBuilder: (context, index) {
                        if (index == _imageGallery.length) {
                          return _buildAddImageButton();
                        }
                        return _buildImageItem(index);
                      },
                    ),
                  ] else ...[
                    _buildEmptyGalleryState(),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Video section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.videocam, color: Color(0xFFFEC53A)),
                      const SizedBox(width: 8),
                      Text(s.propertyVideoOptional),
                      const Spacer(),
                      if (_videoFile != null)
                        const Icon(Icons.check_circle, color: Colors.green),
                    ],
                  ),
                  const SizedBox(height: 16),

                  if (_videoFile != null) ...[
                    _buildVideoPreview(),
                    const SizedBox(height: 16),
                  ],

                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: _pickVideo,
                          icon: const Icon(Icons.video_library),
                          label: Text(_videoFile != null ? 'Change Video' : 'Add Video'),
                        ),
                      ),
                      if (_videoFile != null) ...[
                        const SizedBox(width: 8),
                        IconButton(
                          onPressed: _removeVideo,
                          icon: const Icon(Icons.delete, color: Colors.red),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewCard(String label, String value) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 100,
              child: Text(
                label,
                style: AppTextStyles.font14SemiBold,
              ),
            ),
            Expanded(
              child: Text(
                value.isEmpty ? 'Not provided' : value,
                style: AppTextStyles.font14Regular,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButtons(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  debugPrint('🔙 Back button pressed - Current step: $_currentStep');
                  _previousStep();
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(color: theme.primaryColor, width: 2),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  backgroundColor: Colors.transparent,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.arrow_back, color: theme.primaryColor, size: 18),
                    const SizedBox(width: 8),
                    Text(
                      s.previous,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.primaryColor,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isSubmitting
                  ? null
                  : _currentStep == _totalSteps - 1
                      ? _submitForm
                      : _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.primaryColor,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                elevation: 2,
              ),
              child: _isSubmitting
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.onPrimary),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _currentStep == _totalSteps - 1 ? s.createProperty : s.next,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.onPrimary,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          _currentStep == _totalSteps - 1 ? Icons.check : Icons.arrow_forward,
                          color: theme.colorScheme.onPrimary,
                          size: 18,
                        ),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced selection methods
  Widget _buildEnhancedCategorySelection(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    // Get categories from API data or use empty list if not loaded
    final categories = state is PropertyCreationDataLoaded
        ? state.categories
        : <ServiceCategory>[];

    debugPrint('_buildEnhancedCategorySelection - Categories count: ${categories.length}');

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(Icons.category, color: theme.primaryColor, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  s.selectCategory,
                  style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Category grid
            if (categories.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    children: [
                      if (state is PropertyCreationError) ...[
                        const Icon(Icons.error_outline, color: Colors.red, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load categories',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.red,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            context.read<PropertyCreationCubit>().loadInitialData();
                          },
                          child: const Text('Retry'),
                        ),
                      ] else ...[
                        CircularProgressIndicator(color: theme.primaryColor),
                        const SizedBox(height: 16),
                        Text(
                          'Loading categories...',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              )
            else
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 2.5,
                ),
                itemCount: categories.length,
                itemBuilder: (context, index) {
                  final category = categories[index];
                  final isSelected = _selectedCategory == category.id;

                  return GestureDetector(
                    onTap: () {
                      setState(() => _selectedCategory = category.id);
                      HapticFeedback.selectionClick();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected
                            ? (theme.brightness == Brightness.dark
                                ? const Color(0xFFFFD234).withValues(alpha: 0.15) // Yellow background in dark mode
                                : theme.primaryColor.withValues(alpha: 0.1))
                            : theme.colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected
                              ? (theme.brightness == Brightness.dark
                                  ? const Color(0xFFFFD234) // Yellow border in dark mode
                                  : theme.primaryColor)
                              : theme.dividerColor,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.category,
                            color: isSelected
                                ? (theme.brightness == Brightness.dark
                                    ? const Color(0xFFFFD234) // Yellow in dark mode
                                    : theme.primaryColor)
                                : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              category.title,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: isSelected
                                    ? (theme.brightness == Brightness.dark
                                        ? const Color(0xFFFFD234) // Yellow in dark mode
                                        : theme.primaryColor)
                                    : theme.colorScheme.onSurface.withValues(alpha: 0.8),
                              ),
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedPropertyTypeSelection(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    // Get property types from API data or use empty list if not loaded
    final propertyTypes = state is PropertyCreationDataLoaded
        ? state.propertyTypes
        : <PropertyTypeModel>[];

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(Icons.home_work, color: theme.primaryColor, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  s.propertyType,
                  style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Property type list
            if (propertyTypes.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    children: [
                      if (state is PropertyCreationError) ...[
                        const Icon(Icons.error_outline, color: Colors.red, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load property types',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.red,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            context.read<PropertyCreationCubit>().loadInitialData();
                          },
                          child: const Text('Retry'),
                        ),
                      ] else ...[
                        CircularProgressIndicator(color: theme.primaryColor),
                        const SizedBox(height: 16),
                        Text(
                          'Loading property types...',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              )
            else
              ...propertyTypes.map((type) {
                final isSelected = _selectedPropertyType == type.id;

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: GestureDetector(
                    onTap: () {
                      setState(() => _selectedPropertyType = type.id);
                      HapticFeedback.selectionClick();
                    },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? (theme.brightness == Brightness.dark
                              ? const Color(0xFFFFD234).withValues(alpha: 0.15) // Yellow background in dark mode
                              : theme.primaryColor.withValues(alpha: 0.1))
                          : theme.colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? (theme.brightness == Brightness.dark
                                ? const Color(0xFFFFD234) // Yellow border in dark mode
                                : theme.primaryColor)
                            : theme.dividerColor,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? (theme.brightness == Brightness.dark
                                    ? const Color(0xFFFFD234) // Yellow in dark mode
                                    : theme.primaryColor)
                                : theme.colorScheme.outline,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.home,
                            color: isSelected
                                ? (theme.brightness == Brightness.dark
                                    ? Colors.black // Black icon on yellow background in dark mode
                                    : theme.colorScheme.onPrimary)
                                : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                type.title,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isSelected
                                      ? (theme.brightness == Brightness.dark
                                          ? const Color(0xFFFFD234) // Yellow in dark mode
                                          : theme.primaryColor)
                                      : theme.colorScheme.onSurface.withValues(alpha: 0.8),
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                'Property type option',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            Icons.check_circle,
                            color: theme.brightness == Brightness.dark
                                ? const Color(0xFFFFD234) // Yellow in dark mode
                                : theme.primaryColor,
                            size: 20
                          ),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedCancellationPolicySelection(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    // Get cancellation policies from API data or use empty list if not loaded
    final policies = state is PropertyCreationDataLoaded
        ? state.cancellationPolicies
        : <CancellationPolicyModel>[];

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(Icons.policy, color: theme.primaryColor, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  s.cancellationPolicy,
                  style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Policy list
            if (policies.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    children: [
                      if (state is PropertyCreationError) ...[
                        const Icon(Icons.error_outline, color: Colors.red, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load cancellation policies',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.red,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            context.read<PropertyCreationCubit>().loadInitialData();
                          },
                          child: const Text('Retry'),
                        ),
                      ] else ...[
                        CircularProgressIndicator(color: theme.primaryColor),
                        const SizedBox(height: 16),
                        Text(
                          'Loading cancellation policies...',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              )
            else
              ...policies.map((policy) {
                final isSelected = _selectedCancellationPolicy == policy.id;

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: GestureDetector(
                    onTap: () {
                      setState(() => _selectedCancellationPolicy = policy.id);
                      HapticFeedback.selectionClick();
                    },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? (theme.brightness == Brightness.dark
                              ? const Color(0xFFFFD234).withValues(alpha: 0.15) // Yellow background in dark mode
                              : theme.primaryColor.withValues(alpha: 0.1))
                          : theme.colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? (theme.brightness == Brightness.dark
                                ? const Color(0xFFFFD234) // Yellow border in dark mode
                                : theme.primaryColor)
                            : theme.dividerColor,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? (theme.brightness == Brightness.dark
                                    ? const Color(0xFFFFD234) // Yellow in dark mode
                                    : theme.primaryColor)
                                : theme.colorScheme.outline,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.schedule,
                            color: isSelected
                                ? (theme.brightness == Brightness.dark
                                    ? Colors.black // Black icon on yellow background in dark mode
                                    : theme.colorScheme.onPrimary)
                                : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                policy.name,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isSelected
                                      ? (theme.brightness == Brightness.dark
                                          ? const Color(0xFFFFD234) // Yellow in dark mode
                                          : theme.primaryColor)
                                      : theme.colorScheme.onSurface.withValues(alpha: 0.8),
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                policy.description,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            Icons.check_circle,
                            color: theme.brightness == Brightness.dark
                                ? const Color(0xFFFFD234) // Yellow in dark mode
                                : theme.primaryColor,
                            size: 20
                          ),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildFacilitiesSelection(PropertyCreationState state) {
    // Get facilities from API data or use empty list if not loaded
    final facilities = state is PropertyCreationDataLoaded
        ? state.facilities
        : <FacilityModel>[];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Select Facilities'),
            const SizedBox(height: 8),
            if (facilities.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    children: [
                      if (state is PropertyCreationError) ...[
                        const Icon(Icons.error_outline, color: Colors.red, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load facilities',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.red,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            context.read<PropertyCreationCubit>().loadInitialData();
                          },
                          child: const Text('Retry'),
                        ),
                      ] else ...[
                        CircularProgressIndicator(color: Theme.of(context).primaryColor),
                        const SizedBox(height: 16),
                        Text(
                          'Loading facilities...',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: facilities.map((facility) {
                  final isSelected = _selectedFacilities.contains(facility.id);
                  return FilterChip(
                    label: Text(facility.title),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _selectedFacilities.add(facility.id);
                        } else {
                          _selectedFacilities.remove(facility.id);
                        }
                      });
                    },
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  String _getSelectedFacilitiesNames() {
    if (_selectedFacilities.isEmpty) return 'None selected';

    final currentState = context.read<PropertyCreationCubit>().state;
    if (currentState is PropertyCreationDataLoaded) {
      final facilities = currentState.facilities;
      final selectedNames = _selectedFacilities
          .map((id) => facilities.firstWhere((f) => f.id == id, orElse: () => const FacilityModel(
                id: 0, title: 'Unknown', order: 0)).title)
          .toList();

      return selectedNames.join(', ');
    }

    return '${_selectedFacilities.length} selected';
  }

  String _getSelectedCategoryName() {
    if (_selectedCategory == null) return 'Not selected';

    final currentState = context.read<PropertyCreationCubit>().state;
    if (currentState is PropertyCreationDataLoaded) {
      final category = currentState.categories.firstWhere(
        (c) => c.id == _selectedCategory,
        orElse: () => const ServiceCategory(id: 0, title: 'Unknown', icon: '', image: '', order: 0),
      );
      return category.title;
    }

    return 'Selected';
  }

  String _getSelectedPropertyTypeName() {
    if (_selectedPropertyType == null) return 'Not selected';

    final currentState = context.read<PropertyCreationCubit>().state;
    if (currentState is PropertyCreationDataLoaded) {
      final propertyType = currentState.propertyTypes.firstWhere(
        (pt) => pt.id == _selectedPropertyType,
        orElse: () => const PropertyTypeModel(id: 0, title: 'Unknown', order: 0, createdAt: ''),
      );
      return propertyType.title;
    }

    return 'Selected';
  }

  String _getSelectedCancellationPolicyName() {
    if (_selectedCancellationPolicy == null) return 'Not selected';

    final currentState = context.read<PropertyCreationCubit>().state;
    if (currentState is PropertyCreationDataLoaded) {
      final policy = currentState.cancellationPolicies.firstWhere(
        (cp) => cp.id == _selectedCancellationPolicy,
        orElse: () => const CancellationPolicyModel(
          id: 0, nameEn: 'Unknown', nameAr: '', name: 'Unknown',
          descriptionEn: '', descriptionAr: '', description: '',
          policyType: '', durationType: '', cancellationWindowHours: 0,
          refundPercentage: 0.0, serviceFeeRefundable: false,
          cleaningFeeRefundable: false, isActive: true, order: 0,
          createdAt: '', updatedAt: '',
        ),
      );
      return policy.name;
    }

    return 'Selected';
  }

  // Location picker methods
  void _openLocationPicker() async {
    try {
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => LocationMapPicker(
            initialLatitude: _selectedLocation?.latitude,
            initialLongitude: _selectedLocation?.longitude,
            onLocationSelected: (latitude, longitude) {
              setState(() {
                _selectedLocation = LatLng(latitude, longitude);
                _locationAddress = null; // Will be loaded by reverse geocoding
              });

              // Load address for the selected location
              _loadAddressForLocation(latitude, longitude);

              // Auto-save location data
              _performAutoSave();

              // Show success message
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(S.of(context).locationSelected),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
          ),
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to open location picker: $e')),
        );
      }
    }
  }

  // Load address for selected location
  Future<void> _loadAddressForLocation(double latitude, double longitude) async {
    try {
      final address = await GeocodingService.getAddressFromCoordinates(latitude, longitude);
      if (mounted) {
        setState(() {
          _locationAddress = address ?? 'Location selected';
        });
      }
    } catch (e) {
      // Silently fail - address is not critical
      if (mounted) {
        setState(() {
          _locationAddress = 'Location selected';
        });
      }
    }
  }

  // Gallery methods
  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: _showImagePickerOptions,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!, style: BorderStyle.solid),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_photo_alternate, color: Colors.grey, size: 32),
            SizedBox(height: 4),
            Text('Add Photo', style: TextStyle(color: Colors.grey, fontSize: 12)),
          ],
        ),
      ),
    );
  }

  Widget _buildImageItem(int index) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              _imageGallery[index],
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              cacheWidth: 200, // Optimize memory usage
              cacheHeight: 200,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[300],
                  child: const Icon(Icons.error, color: Colors.red),
                );
              },
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => _removeImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.close, color: Colors.white, size: 16),
            ),
          ),
        ),
        if (index == 0)
          Positioned(
            bottom: 4,
            left: 4,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: const BoxDecoration(
                color: Color(0xFFFEC53A),
                borderRadius: BorderRadius.all(Radius.circular(4)),
              ),
              child: const Text('Main', style: TextStyle(fontSize: 10, fontWeight: FontWeight.bold)),
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyGalleryState() {
    final s = S.of(context);
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!, style: BorderStyle.solid),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.photo_library_outlined, color: Colors.grey, size: 48),
          const SizedBox(height: 8),
          Text(s.noPhotosAdded, style: const TextStyle(color: Colors.grey)),
          const SizedBox(height: 8),
          ElevatedButton.icon(
            onPressed: _showImagePickerOptions,
            icon: const Icon(Icons.add_photo_alternate),
            label: Text(s.addPhotos),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFEC53A),
              foregroundColor: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  void _showImagePickerOptions() {
    final s = S.of(context);
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: Text(s.takePhoto),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromCamera();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: Text(s.chooseFromGallery),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library_outlined),
              title: Text(s.chooseMultiple),
              onTap: () {
                Navigator.pop(context);
                _pickMultipleImages();
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImageFromCamera() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(source: ImageSource.camera);
      if (pickedFile != null) {
        setState(() {
          _imageGallery.add(File(pickedFile.path));
        });
        _performAutoSave();
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to take photo: $e')),
        );
      }
    }
  }

  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);
      if (pickedFile != null) {
        setState(() {
          _imageGallery.add(File(pickedFile.path));
        });
        _performAutoSave();
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to pick image: $e')),
        );
      }
    }
  }

  Future<void> _pickMultipleImages() async {
    try {
      final List<XFile> pickedFiles = await _picker.pickMultiImage();
      if (pickedFiles.isNotEmpty) {
        setState(() {
          for (final file in pickedFiles) {
            if (_imageGallery.length < 10) {
              _imageGallery.add(File(file.path));
            }
          }
        });
        _performAutoSave();
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to pick images: $e')),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _imageGallery.removeAt(index);
    });
    _performAutoSave();
    HapticFeedback.lightImpact();
  }

  // Video methods
  Widget _buildVideoPreview() {
    if (_videoController != null && _videoController!.value.isInitialized) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.black,
        ),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: VideoPlayer(_videoController!),
              ),
            ),
            Positioned(
              bottom: 8,
              right: 8,
              child: FloatingActionButton.small(
                onPressed: () {
                  setState(() {
                    _videoController!.value.isPlaying
                        ? _videoController!.pause()
                        : _videoController!.play();
                  });
                },
                child: Icon(
                  _videoController!.value.isPlaying ? Icons.pause : Icons.play_arrow,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.video_library, size: 48, color: Colors.grey),
            SizedBox(height: 8),
            Text('Video Preview', style: TextStyle(color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Future<void> _pickVideo() async {
    try {
      final XFile? pickedFile = await _picker.pickVideo(source: ImageSource.gallery);
      if (pickedFile != null) {
        setState(() {
          _videoFile = File(pickedFile.path);
        });

        // Initialize video controller
        _videoController?.dispose();
        _videoController = VideoPlayerController.file(_videoFile!)
          ..initialize().then((_) {
            setState(() {});
          });

        _performAutoSave();
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to pick video: $e')),
        );
      }
    }
  }

  void _removeVideo() {
    setState(() {
      _videoFile = null;
      _videoController?.dispose();
      _videoController = null;
    });
    _performAutoSave();
    HapticFeedback.lightImpact();
  }

  void _pickTourismPermitDocument() {
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      backgroundColor: theme.colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: theme.dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            Text(
              'Select Document Source',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose how you want to add your tourism permit document',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Camera option
            _buildDocumentSourceOption(
              icon: Icons.camera_alt,
              title: 'Take Photo',
              subtitle: 'Capture document with camera',
              onTap: () {
                Navigator.pop(context);
                _pickTourismPermitFromCamera();
              },
            ),
            const SizedBox(height: 12),

            // Gallery option
            _buildDocumentSourceOption(
              icon: Icons.photo_library,
              title: 'Choose from Gallery',
              subtitle: 'Select image from gallery',
              onTap: () {
                Navigator.pop(context);
                _pickTourismPermitFromGallery();
              },
            ),
            const SizedBox(height: 12),

            // File picker option
            _buildDocumentSourceOption(
              icon: Icons.folder,
              title: 'Choose File',
              subtitle: 'Select PDF or document file',
              onTap: () {
                Navigator.pop(context);
                _pickTourismPermitFromFiles();
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _removeTourismPermitDocument() {
    setState(() {
      _tourismPermitDocument = null;
    });
  }

  // Helper widget for document source options
  Widget _buildDocumentSourceOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: theme.dividerColor),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: theme.brightness == Brightness.dark
                    ? const Color(0xFFFFD234)
                    : theme.primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ],
        ),
      ),
    );
  }

  // Tourism permit document picking methods
  Future<void> _pickTourismPermitFromCamera() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (pickedFile != null) {
        setState(() {
          _tourismPermitDocument = File(pickedFile.path);
        });

        _showTourismPermitSuccessMessage('Document captured successfully');
        _performAutoSave();
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      debugPrint('Error capturing tourism permit document: $e');
      _showTourismPermitErrorMessage('Failed to capture document: $e');
    }
  }

  Future<void> _pickTourismPermitFromGallery() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (pickedFile != null) {
        setState(() {
          _tourismPermitDocument = File(pickedFile.path);
        });

        _showTourismPermitSuccessMessage('Document selected from gallery');
        _performAutoSave();
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      debugPrint('Error selecting tourism permit document from gallery: $e');
      _showTourismPermitErrorMessage('Failed to select document: $e');
    }
  }

  Future<void> _pickTourismPermitFromFiles() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _tourismPermitDocument = File(result.files.single.path!);
        });

        _showTourismPermitSuccessMessage('Document selected: ${result.files.single.name}');
        _performAutoSave();
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      debugPrint('Error picking tourism permit document file: $e');
      _showTourismPermitErrorMessage('Failed to pick document: $e');
    }
  }

  void _showTourismPermitSuccessMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  message,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.only(bottom: 100, left: 16, right: 16),
        ),
      );
    }
  }

  void _showTourismPermitErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.only(bottom: 100, left: 16, right: 16),
        ),
      );
    }
  }

  // Enhanced UI Components
  Widget _buildEnhancedTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    int? maxLength,
    TextInputType? keyboardType,
    String? prefixText,
  }) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        maxLength: maxLength,
        keyboardType: keyboardType,
        style: theme.textTheme.bodyLarge,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixText: prefixText,
          labelStyle: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          hintStyle: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: theme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: theme.primaryColor, size: 20),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: theme.dividerColor),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: theme.dividerColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: theme.primaryColor, width: 2),
          ),
          filled: true,
          fillColor: theme.colorScheme.surface,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }

}
